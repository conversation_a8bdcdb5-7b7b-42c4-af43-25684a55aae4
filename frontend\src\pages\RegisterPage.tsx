import { useState, useEffect } from 'react'
import { Link as RouterLink, useNavigate } from 'react-router-dom'
import {
  Box,
  Button,
  Container,
  FormControl,
  FormLabel,
  Input,
  VStack,
  Text,
  Heading,
  Link,
  Alert,
  AlertIcon,
  InputGroup,
  InputRightElement,
  IconButton,
  Divider,
  useColorModeValue,
  Flex,
  FormHelperText,
} from '@chakra-ui/react'
import { ViewIcon, ViewOffIcon } from '@chakra-ui/icons'
import { useAuth } from '../contexts/AuthContext'

const RegisterPage = () => {
  const [formData, setFormData] = useState({
    email: '',
    username: '',
    password: '',
    confirmPassword: '',
    fullName: '',
  })
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')

  const { register, user } = useAuth()
  const navigate = useNavigate()

  // Redirect if already logged in
  useEffect(() => {
    if (user) {
      navigate('/dashboard', { replace: true })
    }
  }, [user, navigate])

  const handleInputChange = (field: string) => (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => ({ ...prev, [field]: e.target.value }))
    setError('') // Clear error when user starts typing
  }

  const validateForm = () => {
    if (formData.password !== formData.confirmPassword) {
      setError('Passwords do not match')
      return false
    }
    if (formData.password.length < 8) {
      setError('Password must be at least 8 characters long')
      return false
    }
    if (formData.username.length < 3) {
      setError('Username must be at least 3 characters long')
      return false
    }
    return true
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError('')

    if (!validateForm()) {
      return
    }

    setIsLoading(true)

    try {
      const success = await register(
        formData.email,
        formData.username,
        formData.password,
        formData.fullName || undefined
      )
      if (success) {
        navigate('/login', { replace: true })
      }
    } catch (err: any) {
      setError(err.message || 'Registration failed')
    } finally {
      setIsLoading(false)
    }
  }

  const bgGradient = useColorModeValue(
    'linear(to-br, blue.50, purple.50)',
    'linear(to-br, gray.900, gray.800)'
  )

  return (
    <Box minH="100vh" bg={bgGradient}>
      <Container maxW="md" centerContent>
        <Flex direction="column" align="center" justify="center" minH="100vh" w="full" py={8}>
          <VStack spacing={8} w="full">
            <VStack spacing={4}>
              <Box
                w={16}
                h={16}
                bg="brand.500"
                borderRadius="xl"
                display="flex"
                alignItems="center"
                justifyContent="center"
                boxShadow="lg"
              >
                <Text fontSize="2xl" fontWeight="bold" color="white">
                  E
                </Text>
              </Box>
              <VStack spacing={2}>
                <Heading size="xl" textAlign="center" color="gray.100">
                  Create your account
                </Heading>
                <Text color="gray.400" textAlign="center">
                  Join Expendra and start your AI-powered purchasing journey
                </Text>
              </VStack>
            </VStack>

            <Box
              w="full"
              bg="gray.800"
              p={8}
              borderRadius="2xl"
              boxShadow="2xl"
              border="1px"
              borderColor="gray.700"
            >
              <form onSubmit={handleSubmit}>
                <VStack spacing={5}>
                  {error && (
                    <Alert status="error" borderRadius="lg">
                      <AlertIcon />
                      {error}
                    </Alert>
                  )}

                  <FormControl isRequired>
                    <FormLabel color="gray.300">Email address</FormLabel>
                    <Input
                      type="email"
                      value={formData.email}
                      onChange={handleInputChange('email')}
                      placeholder="Enter your email"
                      size="lg"
                      bg="gray.700"
                      border="1px"
                      borderColor="gray.600"
                      _hover={{ borderColor: 'gray.500' }}
                      _focus={{ borderColor: 'brand.500', boxShadow: '0 0 0 1px var(--chakra-colors-brand-500)' }}
                    />
                  </FormControl>

                  <FormControl isRequired>
                    <FormLabel color="gray.300">Username</FormLabel>
                    <Input
                      type="text"
                      value={formData.username}
                      onChange={handleInputChange('username')}
                      placeholder="Choose a username"
                      size="lg"
                      bg="gray.700"
                      border="1px"
                      borderColor="gray.600"
                      _hover={{ borderColor: 'gray.500' }}
                      _focus={{ borderColor: 'brand.500', boxShadow: '0 0 0 1px var(--chakra-colors-brand-500)' }}
                    />
                    <FormHelperText color="gray.500">
                      At least 3 characters, letters and numbers only
                    </FormHelperText>
                  </FormControl>

                  <FormControl>
                    <FormLabel color="gray.300">Full Name (Optional)</FormLabel>
                    <Input
                      type="text"
                      value={formData.fullName}
                      onChange={handleInputChange('fullName')}
                      placeholder="Enter your full name"
                      size="lg"
                      bg="gray.700"
                      border="1px"
                      borderColor="gray.600"
                      _hover={{ borderColor: 'gray.500' }}
                      _focus={{ borderColor: 'brand.500', boxShadow: '0 0 0 1px var(--chakra-colors-brand-500)' }}
                    />
                  </FormControl>

                  <FormControl isRequired>
                    <FormLabel color="gray.300">Password</FormLabel>
                    <InputGroup size="lg">
                      <Input
                        type={showPassword ? 'text' : 'password'}
                        value={formData.password}
                        onChange={handleInputChange('password')}
                        placeholder="Create a password"
                        bg="gray.700"
                        border="1px"
                        borderColor="gray.600"
                        _hover={{ borderColor: 'gray.500' }}
                        _focus={{ borderColor: 'brand.500', boxShadow: '0 0 0 1px var(--chakra-colors-brand-500)' }}
                      />
                      <InputRightElement>
                        <IconButton
                          aria-label={showPassword ? 'Hide password' : 'Show password'}
                          icon={showPassword ? <ViewOffIcon /> : <ViewIcon />}
                          onClick={() => setShowPassword(!showPassword)}
                          variant="ghost"
                          size="sm"
                          color="gray.400"
                          _hover={{ color: 'gray.300' }}
                        />
                      </InputRightElement>
                    </InputGroup>
                    <FormHelperText color="gray.500">
                      At least 8 characters
                    </FormHelperText>
                  </FormControl>

                  <FormControl isRequired>
                    <FormLabel color="gray.300">Confirm Password</FormLabel>
                    <InputGroup size="lg">
                      <Input
                        type={showConfirmPassword ? 'text' : 'password'}
                        value={formData.confirmPassword}
                        onChange={handleInputChange('confirmPassword')}
                        placeholder="Confirm your password"
                        bg="gray.700"
                        border="1px"
                        borderColor="gray.600"
                        _hover={{ borderColor: 'gray.500' }}
                        _focus={{ borderColor: 'brand.500', boxShadow: '0 0 0 1px var(--chakra-colors-brand-500)' }}
                      />
                      <InputRightElement>
                        <IconButton
                          aria-label={showConfirmPassword ? 'Hide password' : 'Show password'}
                          icon={showConfirmPassword ? <ViewOffIcon /> : <ViewIcon />}
                          onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                          variant="ghost"
                          size="sm"
                          color="gray.400"
                          _hover={{ color: 'gray.300' }}
                        />
                      </InputRightElement>
                    </InputGroup>
                  </FormControl>

                  <Button
                    type="submit"
                    size="lg"
                    w="full"
                    bg="brand.500"
                    color="white"
                    _hover={{ bg: 'brand.600', transform: 'translateY(-1px)' }}
                    _active={{ bg: 'brand.700', transform: 'translateY(0)' }}
                    isLoading={isLoading}
                    loadingText="Creating account..."
                    borderRadius="lg"
                    fontWeight="semibold"
                    transition="all 0.2s"
                  >
                    Create account
                  </Button>

                  <Divider borderColor="gray.600" />

                  <Text color="gray.400" textAlign="center">
                    Already have an account?{' '}
                    <Link
                      as={RouterLink}
                      to="/login"
                      color="brand.400"
                      fontWeight="semibold"
                      _hover={{ color: 'brand.300', textDecoration: 'underline' }}
                    >
                      Sign in
                    </Link>
                  </Text>
                </VStack>
              </form>
            </Box>
          </VStack>
        </Flex>
      </Container>
    </Box>
  )
}

export default RegisterPage
