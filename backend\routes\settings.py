"""
User settings routes for Expendra API.
"""
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from database import get_db
from models import User, UserSettings
from schemas import (
    UserSettingsResponse, UserSettingsUpdate, MessageResponse
)
from auth import get_current_active_user

router = APIRouter(prefix="/settings", tags=["User Settings"])

@router.get("/", response_model=UserSettingsResponse)
async def get_user_settings(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Get current user's settings.
    """
    settings = db.query(UserSettings).filter(UserSettings.user_id == current_user.id).first()
    
    if not settings:
        # Create default settings if they don't exist
        settings = UserSettings(
            user_id=current_user.id,
            default_llm_provider="openai",
            default_model="gpt-3.5-turbo",
            theme="dark",
            primary_color="blue",
            font_size="medium",
            animations_enabled=True,
            default_research_depth="standard",
            auto_export_format="pdf"
        )
        db.add(settings)
        db.commit()
        db.refresh(settings)
    
    return settings

@router.put("/", response_model=UserSettingsResponse)
async def update_user_settings(
    settings_update: UserSettingsUpdate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Update current user's settings.
    """
    settings = db.query(UserSettings).filter(UserSettings.user_id == current_user.id).first()
    
    if not settings:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User settings not found"
        )
    
    # Update only provided fields
    update_data = settings_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(settings, field, value)
    
    db.commit()
    db.refresh(settings)
    
    return settings

@router.post("/reset", response_model=MessageResponse)
async def reset_user_settings(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Reset user settings to default values.
    """
    settings = db.query(UserSettings).filter(UserSettings.user_id == current_user.id).first()
    
    if not settings:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User settings not found"
        )
    
    # Reset to default values
    settings.default_llm_provider = "openai"
    settings.openai_api_key = None
    settings.anthropic_api_key = None
    settings.google_api_key = None
    settings.ollama_endpoint = None
    settings.default_model = "gpt-3.5-turbo"
    settings.theme = "dark"
    settings.primary_color = "blue"
    settings.font_size = "medium"
    settings.animations_enabled = True
    settings.default_research_depth = "standard"
    settings.auto_export_format = "pdf"
    
    db.commit()
    
    return MessageResponse(
        message="Settings reset to default values",
        success=True
    )

@router.get("/llm-providers", response_model=dict)
async def get_available_llm_providers():
    """
    Get list of available LLM providers and their supported models.
    """
    providers = {
        "openai": {
            "name": "OpenAI",
            "models": [
                "gpt-4",
                "gpt-4-turbo-preview",
                "gpt-3.5-turbo",
                "gpt-3.5-turbo-16k"
            ],
            "requires_api_key": True
        },
        "anthropic": {
            "name": "Anthropic",
            "models": [
                "claude-3-opus-20240229",
                "claude-3-sonnet-20240229",
                "claude-3-haiku-20240307",
                "claude-2.1",
                "claude-2.0"
            ],
            "requires_api_key": True
        },
        "google": {
            "name": "Google",
            "models": [
                "gemini-pro",
                "gemini-pro-vision",
                "text-bison-001",
                "chat-bison-001"
            ],
            "requires_api_key": True
        },
        "ollama": {
            "name": "Ollama (Local)",
            "models": [
                "llama2",
                "codellama",
                "mistral",
                "neural-chat",
                "starling-lm"
            ],
            "requires_api_key": False,
            "requires_endpoint": True
        }
    }
    
    return {
        "providers": providers,
        "default_provider": "openai",
        "default_model": "gpt-3.5-turbo"
    }
