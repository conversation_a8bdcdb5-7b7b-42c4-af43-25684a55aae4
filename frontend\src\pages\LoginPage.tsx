import { useState, useEffect } from 'react'
import { Link as RouterLink, useNavigate, useLocation } from 'react-router-dom'
import {
  Box,
  Button,
  Container,
  FormControl,
  FormLabel,
  Input,
  VStack,
  Text,
  Heading,
  Link,
  Alert,
  AlertIcon,
  InputGroup,
  InputRightElement,
  IconButton,
  Divider,
  useColorModeValue,
  Flex,
  Image,
} from '@chakra-ui/react'
import { ViewIcon, ViewOffIcon } from '@chakra-ui/icons'
import { useAuth } from '../contexts/AuthContext'

const LoginPage = () => {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [showPassword, setShowPassword] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')

  const { login, user } = useAuth()
  const navigate = useNavigate()
  const location = useLocation()

  const from = location.state?.from?.pathname || '/dashboard'

  // Redirect if already logged in
  useEffect(() => {
    if (user) {
      navigate(from, { replace: true })
    }
  }, [user, navigate, from])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError('')
    setIsLoading(true)

    try {
      const success = await login(email, password)
      if (success) {
        navigate(from, { replace: true })
      }
    } catch (err: any) {
      setError(err.message || 'Login failed')
    } finally {
      setIsLoading(false)
    }
  }

  const bgGradient = useColorModeValue(
    'linear(to-br, blue.50, purple.50)',
    'linear(to-br, gray.900, gray.800)'
  )

  return (
    <Box minH="100vh" bg={bgGradient}>
      <Container maxW="md" centerContent>
        <Flex direction="column" align="center" justify="center" minH="100vh" w="full">
          {/* Logo and Brand */}
          <VStack spacing={8} w="full">
            <VStack spacing={4}>
              <Box
                w={16}
                h={16}
                bg="brand.500"
                borderRadius="xl"
                display="flex"
                alignItems="center"
                justifyContent="center"
                boxShadow="lg"
              >
                <Text fontSize="2xl" fontWeight="bold" color="white">
                  E
                </Text>
              </Box>
              <VStack spacing={2}>
                <Heading size="xl" textAlign="center" color="gray.100">
                  Welcome back
                </Heading>
                <Text color="gray.400" textAlign="center">
                  Sign in to your Expendra account
                </Text>
              </VStack>
            </VStack>

            {/* Login Form */}
            <Box
              w="full"
              bg="gray.800"
              p={8}
              borderRadius="2xl"
              boxShadow="2xl"
              border="1px"
              borderColor="gray.700"
            >
              <form onSubmit={handleSubmit}>
                <VStack spacing={6}>
                  {error && (
                    <Alert status="error" borderRadius="lg">
                      <AlertIcon />
                      {error}
                    </Alert>
                  )}

                  <FormControl isRequired>
                    <FormLabel color="gray.300">Email address</FormLabel>
                    <Input
                      type="email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      placeholder="Enter your email"
                      size="lg"
                      bg="gray.700"
                      border="1px"
                      borderColor="gray.600"
                      _hover={{ borderColor: 'gray.500' }}
                      _focus={{ borderColor: 'brand.500', boxShadow: '0 0 0 1px var(--chakra-colors-brand-500)' }}
                    />
                  </FormControl>

                  <FormControl isRequired>
                    <FormLabel color="gray.300">Password</FormLabel>
                    <InputGroup size="lg">
                      <Input
                        type={showPassword ? 'text' : 'password'}
                        value={password}
                        onChange={(e) => setPassword(e.target.value)}
                        placeholder="Enter your password"
                        bg="gray.700"
                        border="1px"
                        borderColor="gray.600"
                        _hover={{ borderColor: 'gray.500' }}
                        _focus={{ borderColor: 'brand.500', boxShadow: '0 0 0 1px var(--chakra-colors-brand-500)' }}
                      />
                      <InputRightElement>
                        <IconButton
                          aria-label={showPassword ? 'Hide password' : 'Show password'}
                          icon={showPassword ? <ViewOffIcon /> : <ViewIcon />}
                          onClick={() => setShowPassword(!showPassword)}
                          variant="ghost"
                          size="sm"
                          color="gray.400"
                          _hover={{ color: 'gray.300' }}
                        />
                      </InputRightElement>
                    </InputGroup>
                  </FormControl>

                  <Button
                    type="submit"
                    size="lg"
                    w="full"
                    bg="brand.500"
                    color="white"
                    _hover={{ bg: 'brand.600', transform: 'translateY(-1px)' }}
                    _active={{ bg: 'brand.700', transform: 'translateY(0)' }}
                    isLoading={isLoading}
                    loadingText="Signing in..."
                    borderRadius="lg"
                    fontWeight="semibold"
                    transition="all 0.2s"
                  >
                    Sign in
                  </Button>

                  <Divider borderColor="gray.600" />

                  <Text color="gray.400" textAlign="center">
                    Don't have an account?{' '}
                    <Link
                      as={RouterLink}
                      to="/register"
                      color="brand.400"
                      fontWeight="semibold"
                      _hover={{ color: 'brand.300', textDecoration: 'underline' }}
                    >
                      Sign up
                    </Link>
                  </Text>
                </VStack>
              </form>
            </Box>
          </VStack>
        </Flex>
      </Container>
    </Box>
  )
}

export default LoginPage
