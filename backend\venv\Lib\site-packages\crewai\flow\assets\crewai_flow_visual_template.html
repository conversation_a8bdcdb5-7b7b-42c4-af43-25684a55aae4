<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <title>{{ title }}</title>
    <script
      src="https://cdnjs.cloudflare.com/ajax/libs/vis-network/9.1.2/dist/vis-network.min.js"
      integrity="sha512-LnvoEWDFrqGHlHmDD2101OrLcbsfkrzoSpvtSQtxK3RMnRV0eOkhhBN2dXHKRrUU8p2DGRTk35n4O8nWSVe1mQ=="
      crossorigin="anonymous"
      referrerpolicy="no-referrer"
    ></script>
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/vis-network/9.1.2/dist/dist/vis-network.min.css"
      integrity="sha512-WgxfT5LWjfszlPHXRmBWHkV2eceiWTOBvrKCNbdgDYTHrT2AeLCGbF4sZlZw3UMN3WtL0tGUoIAKsu8mllg/XA=="
      crossorigin="anonymous"
      referrerpolicy="no-referrer"
    />
    <style type="text/css">
      body {
        font-family: verdana;
        margin: 0;
        padding: 0;
      }
      .container {
        display: flex;
        flex-direction: column;
        height: 100vh;
      }
      #mynetwork {
        flex-grow: 1;
        width: 100%;
        height: 750px;
        background-color: #ffffff;
      }
      .card {
        border: none;
      }
      .legend-container {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 10px;
        background-color: #f8f9fa;
        position: fixed; /* Make the legend fixed */
        bottom: 0; /* Position it at the bottom */
        width: 100%; /* Make it span the full width */
      }
      .legend-item {
        display: flex;
        align-items: center;
        margin-right: 20px;
      }
      .legend-color-box {
        width: 20px;
        height: 20px;
        margin-right: 5px;
      }
      .logo {
        height: 50px;
        margin-right: 20px;
      }
      .legend-dashed {
        border-bottom: 2px dashed #666666;
        width: 20px;
        height: 0;
        margin-right: 5px;
      }
      .legend-solid {
        border-bottom: 2px solid #666666;
        width: 20px;
        height: 0;
        margin-right: 5px;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="card" style="width: 100%">
        <div id="mynetwork" class="card-body"></div>
      </div>
      <div class="legend-container">
        <img
          src="data:image/svg+xml;base64,{{ logo_svg_base64 }}"
          alt="CrewAI logo"
          class="logo"
        />
        <!-- LEGEND_ITEMS_PLACEHOLDER -->
      </div>
    </div>
    {{ network_content }}
  </body>
</html>
