import {
  Box,
  Container,
  Heading,
  Text,
  VStack,
  Button,
  Card,
  CardBody,
} from '@chakra-ui/react'
import { useNavigate } from 'react-router-dom'

const ResearchPage = () => {
  const navigate = useNavigate()

  return (
    <Box minH="100vh" bg="gray.900" p={6}>
      <Container maxW="4xl">
        <VStack spacing={8}>
          <VStack spacing={4} textAlign="center">
            <Heading size="xl" color="gray.100">
              Deep Market Research
            </Heading>
            <Text color="gray.400" fontSize="lg">
              Comprehensive research and analysis powered by AI agents
            </Text>
          </VStack>

          <Card bg="gray.800" borderColor="gray.700" w="full">
            <CardBody>
              <VStack spacing={6}>
                <Text color="gray.300" textAlign="center" fontSize="lg">
                  🔬 Research functionality coming soon!
                </Text>
                <Text color="gray.400" textAlign="center">
                  This page will feature the deep research interface where you can:
                </Text>
                <VStack spacing={2} color="gray.400" align="start">
                  <Text>• Conduct comprehensive market research</Text>
                  <Text>• Monitor ongoing research assignments</Text>
                  <Text>• View detailed research findings and reports</Text>
                  <Text>• Export results in PDF and Excel formats</Text>
                  <Text>• Track research progress and status</Text>
                </VStack>
                <Button
                  colorScheme="brand"
                  onClick={() => navigate('/dashboard')}
                >
                  Back to Dashboard
                </Button>
              </VStack>
            </CardBody>
          </Card>
        </VStack>
      </Container>
    </Box>
  )
}

export default ResearchPage
