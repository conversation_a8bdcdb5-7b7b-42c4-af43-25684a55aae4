import {
  Box,
  Container,
  Heading,
  Text,
  VStack,
  HStack,
  Button,
  Card,
  CardBody,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  Grid,
  GridItem,
  Badge,
  Avatar,
  Menu,
  MenuButton,
  MenuList,
  MenuItem,
  IconButton,
} from '@chakra-ui/react'
import { FaChevronDown, FaCog, FaSearch, FaFileAlt } from 'react-icons/fa'
import { useAuth } from '../contexts/AuthContext'
import { useNavigate } from 'react-router-dom'

const DashboardPage = () => {
  const { user, logout } = useAuth()
  const navigate = useNavigate()

  return (
    <Box minH="100vh" bg="gray.900">
      {/* Header */}
      <Box bg="gray.800" borderBottom="1px" borderColor="gray.700" px={6} py={4}>
        <Container maxW="7xl">
          <HStack justify="space-between">
            <HStack spacing={4}>
              <Box
                w={10}
                h={10}
                bg="brand.500"
                borderRadius="lg"
                display="flex"
                alignItems="center"
                justifyContent="center"
              >
                <Text fontSize="lg" fontWeight="bold" color="white">
                  E
                </Text>
              </Box>
              <Heading size="lg" color="gray.100">
                Expendra
              </Heading>
            </HStack>

            <HStack spacing={4}>
              <Button
                leftIcon={<SearchIcon />}
                variant="ghost"
                color="gray.300"
                _hover={{ bg: 'gray.700' }}
                onClick={() => navigate('/search')}
              >
                Search
              </Button>
              <Button
                leftIcon={<FileTextIcon />}
                variant="ghost"
                color="gray.300"
                _hover={{ bg: 'gray.700' }}
                onClick={() => navigate('/research')}
              >
                Research
              </Button>

              <Menu>
                <MenuButton
                  as={Button}
                  variant="ghost"
                  rightIcon={<ChevronDownIcon />}
                  color="gray.300"
                  _hover={{ bg: 'gray.700' }}
                >
                  <HStack spacing={2}>
                    <Avatar size="sm" name={user?.full_name || user?.username} />
                    <Text>{user?.username}</Text>
                  </HStack>
                </MenuButton>
                <MenuList bg="gray.800" borderColor="gray.700">
                  <MenuItem
                    bg="gray.800"
                    _hover={{ bg: 'gray.700' }}
                    icon={<SettingsIcon />}
                    onClick={() => navigate('/settings')}
                  >
                    Settings
                  </MenuItem>
                  <MenuItem
                    bg="gray.800"
                    _hover={{ bg: 'gray.700' }}
                    onClick={logout}
                  >
                    Sign out
                  </MenuItem>
                </MenuList>
              </Menu>
            </HStack>
          </HStack>
        </Container>
      </Box>

      {/* Main Content */}
      <Container maxW="7xl" py={8}>
        <VStack spacing={8} align="stretch">
          {/* Welcome Section */}
          <Box>
            <Heading size="xl" color="gray.100" mb={2}>
              Welcome back, {user?.full_name || user?.username}!
            </Heading>
            <Text color="gray.400" fontSize="lg">
              Your AI-powered purchasing assistant is ready to help you find parts, tools, and materials.
            </Text>
          </Box>

          {/* Quick Actions */}
          <Grid templateColumns="repeat(auto-fit, minmax(300px, 1fr))" gap={6}>
            <Card bg="gray.800" borderColor="gray.700">
              <CardBody>
                <VStack align="start" spacing={4}>
                  <Box>
                    <Heading size="md" color="gray.100" mb={2}>
                      Start New Search
                    </Heading>
                    <Text color="gray.400">
                      Search for parts, tools, or materials using natural language
                    </Text>
                  </Box>
                  <Button
                    colorScheme="brand"
                    size="lg"
                    w="full"
                    onClick={() => navigate('/search')}
                  >
                    New Search
                  </Button>
                </VStack>
              </CardBody>
            </Card>

            <Card bg="gray.800" borderColor="gray.700">
              <CardBody>
                <VStack align="start" spacing={4}>
                  <Box>
                    <Heading size="md" color="gray.100" mb={2}>
                      Deep Research
                    </Heading>
                    <Text color="gray.400">
                      Conduct comprehensive market research and analysis
                    </Text>
                  </Box>
                  <Button
                    variant="outline"
                    colorScheme="brand"
                    size="lg"
                    w="full"
                    onClick={() => navigate('/research')}
                  >
                    Start Research
                  </Button>
                </VStack>
              </CardBody>
            </Card>
          </Grid>

          {/* Stats Overview */}
          <Grid templateColumns="repeat(auto-fit, minmax(200px, 1fr))" gap={6}>
            <Card bg="gray.800" borderColor="gray.700">
              <CardBody>
                <Stat>
                  <StatLabel color="gray.400">Total Searches</StatLabel>
                  <StatNumber color="gray.100">0</StatNumber>
                  <StatHelpText color="gray.500">This month</StatHelpText>
                </Stat>
              </CardBody>
            </Card>

            <Card bg="gray.800" borderColor="gray.700">
              <CardBody>
                <Stat>
                  <StatLabel color="gray.400">Research Sessions</StatLabel>
                  <StatNumber color="gray.100">0</StatNumber>
                  <StatHelpText color="gray.500">Active</StatHelpText>
                </Stat>
              </CardBody>
            </Card>

            <Card bg="gray.800" borderColor="gray.700">
              <CardBody>
                <Stat>
                  <StatLabel color="gray.400">Reports Generated</StatLabel>
                  <StatNumber color="gray.100">0</StatNumber>
                  <StatHelpText color="gray.500">All time</StatHelpText>
                </Stat>
              </CardBody>
            </Card>
          </Grid>

          {/* Recent Activity */}
          <Card bg="gray.800" borderColor="gray.700">
            <CardBody>
              <Heading size="md" color="gray.100" mb={4}>
                Recent Activity
              </Heading>
              <VStack spacing={4} align="stretch">
                <Box
                  p={4}
                  bg="gray.700"
                  borderRadius="lg"
                  textAlign="center"
                  color="gray.400"
                >
                  <Text>No recent activity</Text>
                  <Text fontSize="sm" mt={2}>
                    Start your first search to see activity here
                  </Text>
                </Box>
              </VStack>
            </CardBody>
          </Card>
        </VStack>
      </Container>
    </Box>
  )
}

export default DashboardPage
