import {
  Box,
  Container,
  Heading,
  Text,
  VStack,
  Button,
  Card,
  CardBody,
} from '@chakra-ui/react'
import { useNavigate } from 'react-router-dom'

const SearchPage = () => {
  const navigate = useNavigate()

  return (
    <Box minH="100vh" bg="gray.900" p={6}>
      <Container maxW="4xl">
        <VStack spacing={8}>
          <VStack spacing={4} textAlign="center">
            <Heading size="xl" color="gray.100">
              AI-Powered Search
            </Heading>
            <Text color="gray.400" fontSize="lg">
              Search for parts, tools, and materials using natural language
            </Text>
          </VStack>

          <Card bg="gray.800" borderColor="gray.700" w="full">
            <CardBody>
              <VStack spacing={6}>
                <Text color="gray.300" textAlign="center" fontSize="lg">
                  🚧 Search functionality coming soon!
                </Text>
                <Text color="gray.400" textAlign="center">
                  This page will feature the AI-powered search interface where you can:
                </Text>
                <VStack spacing={2} color="gray.400" align="start">
                  <Text>• Search for aerospace parts and FAA PMA alternatives</Text>
                  <Text>• Find tools and materials from multiple vendors</Text>
                  <Text>• Get real-time pricing information</Text>
                  <Text>• Discover compatible substitute part numbers</Text>
                </VStack>
                <Button
                  colorScheme="brand"
                  onClick={() => navigate('/dashboard')}
                >
                  Back to Dashboard
                </Button>
              </VStack>
            </CardBody>
          </Card>
        </VStack>
      </Container>
    </Box>
  )
}

export default SearchPage
